import { db } from "$/lib/server/db";
import { characterTable } from "$/lib/server/db/schema";
import {type RequestHandler, error } from "@sveltejs/kit";

export const PUT: RequestHandler = async ({ request }) => {
    const {
        name,
        targetLength,
        offsetPos,
        offsetScale,
        referenceCurve,
    } = await request.json();

    if (typeof name !== "string") return error(400);

    const rows = await db.insert(characterTable)
        .values({
            name,
            referenceCurve,
            targetLength,
            offsetPos,
            offsetScale,
        })
        .returning({
            id: characterTable.id,
        });
    if (rows.length !== 1) return error(500);
    
    const id = rows[0].id;

    return new Response(JSON.stringify({
        id,
    }));
};